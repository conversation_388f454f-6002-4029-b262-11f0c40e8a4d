
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>A<PERSON>s, Tooltip, ResponsiveContainer, Cell, LabelList } from 'recharts';
import { ChartData } from '../types';

interface RecentAnswersChartProps {
  data: ChartData[];
  correctAnswerName: string;
}

const RecentAnswersChart: React.FC<RecentAnswersChartProps> = ({ data, correctAnswerName }) => {
  return (
    <div className="w-full h-full bg-white/90 rounded-lg p-4 shadow-lg">
      <h3 className="text-center font-bold text-xl mb-2 text-gray-700">Recent Answers</h3>
      <ResponsiveContainer width="100%" height="90%">
        <BarChart data={data} margin={{ top: 20, right: 20, left: 0, bottom: 5 }}>
          <XAxis dataKey="name" tick={{ fill: '#4A5568' }} />
          <YAxis domain={[0, 50]} tick={{ fill: '#4A5568' }} />
          <Tooltip 
            cursor={{fill: 'rgba(200,200,200,0.3)'}}
            contentStyle={{background: 'rgba(255,255,255,0.8)', border: '1px solid #ccc', borderRadius: '5px'}}
          />
          <Bar dataKey="value">
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.name === correctAnswerName ? '#10B981' : '#F87171'} />
            ))}
            <LabelList dataKey="value" position="top" formatter={(value: number) => `${value}%`} style={{ fill: '#374151', fontWeight: 'bold' }} />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RecentAnswersChart;
