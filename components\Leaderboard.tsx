
import React from 'react';
import { Player } from '../types';

interface LeaderboardProps {
  players: Player[];
}

const Leaderboard: React.FC<LeaderboardProps> = ({ players }) => {
  return (
    <div className="w-64 h-full bg-slate-800/50 backdrop-blur-md border border-slate-500/50 rounded-2xl shadow-xl p-4 flex flex-col text-white">
      <h2 className="text-2xl font-bold mb-4 text-center border-b-2 border-slate-600 pb-2">Fastest Fingers</h2>
      <ul className="space-y-2 flex-grow">
        {players.map((player, index) => (
          <li key={index} className="bg-gray-600/80 rounded-lg p-3 font-medium shadow-md flex items-center">
            <span className="mr-3 font-bold">{index + 1}.</span>
            {player.name}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Leaderboard;
