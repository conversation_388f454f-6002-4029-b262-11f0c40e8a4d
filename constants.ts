
import { Question, Player, QuestionStatus } from './types';

export const QUIZ_QUESTIONS: Question[] = [
  {
    id: 1,
    question: "What is the most common blood type in humans?",
    options: ["A+", "B-", "O+", "AB+"],
    correctAnswerIndex: 2,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 2,
    question: "Which bone is the longest in the human body?",
    options: ["Femur", "Tibia", "Humerus", "Fibula"],
    correctAnswerIndex: 0,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 3,
    question: "What does 'MRI' stand for in medical imaging?",
    options: ["Medical Resonance Imaging", "Magnetic Resonance Imaging", "Molecular Response Imaging", "Magnetic Resonance Iconography"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 4,
    question: "How many chambers are in the human heart?",
    options: ["Two", "Three", "Four", "Five"],
    correctAnswerIndex: 2,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 5,
    question: "Which gland is known as the 'master gland' of the endocrine system?",
    options: ["Thyroid Gland", "Adrenal Gland", "Pancreas", "Pituitary Gland"],
    correctAnswerIndex: 3,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 6,
    question: "Hepatitis is an inflammation of which organ?",
    options: ["Kidney", "Lungs", "Liver", "Stomach"],
    correctAnswerIndex: 2,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 7,
    question: "What is the medical term for a 'common cold'?",
    options: ["Influenza", "Acute Viral Nasopharyngitis", "Bronchitis", "Pneumonia"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 8,
    question: "Which of these is NOT a part of the small intestine?",
    options: ["Duodenum", "Jejunum", "Ileum", "Cecum"],
    correctAnswerIndex: 3,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 9,
    question: "What is the normal range for human body temperature in Celsius?",
    options: ["35.5 - 36.5 °C", "36.5 - 37.5 °C", "37.5 - 38.5 °C", "38.5 - 39.5 °C"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 10,
    question: "When was the Indian Medical Association founded?",
    options: ["1930", "1928", "1905", "1947"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
];


export const FASTEST_FINGERS: Player[] = [
    { name: "Dr. Priya" },
    { name: "Dr. Pratik" },
    { name: "Dr. Maruti" },
    { name: "Dr. Omkar" },
    { name: "Dr. Ravikant" },
];

export const QUESTION_TIMER_DURATION = 15;
export const RESULTS_VIEW_DURATION = 5;
