
import React, { useEffect, useRef } from 'react';
import { Question, QuestionStatus } from '../types';

interface QuestionsPanelProps {
  questions: Question[];
  currentQuestionIndex: number;
  score: number;
}

const QuestionsPanel: React.FC<QuestionsPanelProps> = ({ questions, currentQuestionIndex, score }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const currentQuestionRef = useRef<HTMLButtonElement>(null);

  // Initial scroll to top (Question 1)
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;
    }
  }, []);

  // Auto-scroll to current question
  useEffect(() => {
    if (currentQuestionRef.current && scrollContainerRef.current) {
      currentQuestionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }, [currentQuestionIndex]);

  const getButtonClass = (question: Question, index: number) => {
    let baseClass = "w-full text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-all duration-300 transform hover:scale-105 border-2 border-transparent";
    
    if (question.status === QuestionStatus.CORRECT) {
      return `${baseClass} bg-green-600/80 border-green-400`;
    }
    if (question.status === QuestionStatus.INCORRECT) {
      return `${baseClass} bg-red-600/80 border-red-400`;
    }
    if (index === currentQuestionIndex) {
      return `${baseClass} bg-blue-600/80 border-blue-400 animate-pulse`;
    }
    return `${baseClass} bg-gray-600/80 hover:bg-gray-500/80`;
  };

  return (
    <div className="w-64 h-full bg-slate-800/50 backdrop-blur-md border border-slate-500/50 rounded-2xl shadow-xl p-4 flex flex-col text-white">
      <h2 className="text-2xl font-bold mb-4 text-center border-b-2 border-slate-600 pb-2">Questions</h2>
      <p className="text-sm text-gray-300 mb-4 text-center">(10 Points For Each)</p>
      <div ref={scrollContainerRef} className="flex-grow overflow-y-auto custom-scrollbar">
        <div className="space-y-2 flex flex-col">
          {questions.map((q, index) => (
            <button
              key={q.id}
              ref={index === currentQuestionIndex ? currentQuestionRef : null}
              className={getButtonClass(q, index)}
              disabled
            >
              Question {q.id}
            </button>
          ))}
        </div>
      </div>
      <div className="mt-4 pt-4 border-t-2 border-slate-600 flex-shrink-0">
        <p className="text-2xl font-bold text-center">Score: {score}</p>
      </div>
    </div>
  );
};

export default QuestionsPanel;
