import React, { useState, useEffect, useCallback } from 'react';
import { QUIZ_QUESTIONS, FASTEST_FINGERS, QUESTION_TIMER_DURATION, RESULTS_VIEW_DURATION } from './constants';
import { Question, QuestionStatus, ChartData } from './types';
import QuestionsPanel from './components/QuestionsPanel';
import Leaderboard from './components/Leaderboard';
import RecentAnswersChart from './components/RecentAnswersChart';
import CheckIcon from './components/CheckIcon';

enum GameState {
  PLAYING,
  ANSWERED,
  FINISHED,
}

const App: React.FC = () => {
  const [isGameStarted, setIsGameStarted] = useState<boolean>(false);
  const [email, setEmail] = useState<string>('');
  const [emailError, setEmailError] = useState<string>('');
  
  const [questions, setQuestions] = useState<Question[]>(QUIZ_QUESTIONS);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [score, setScore] = useState<number>(0);
  const [gameState, setGameState] = useState<GameState>(GameState.PLAYING);
  const [selectedAnswerIndex, setSelectedAnswerIndex] = useState<number | null>(null);
  const [timer, setTimer] = useState<number>(QUESTION_TIMER_DURATION);
  const [chartData, setChartData] = useState<ChartData[]>([]);

  const currentQuestion = questions[currentQuestionIndex];

  useEffect(() => {
    // Apply consistent light gray gradient background to all pages
    document.body.style.backgroundImage = "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)";
    document.body.style.backgroundAttachment = "fixed";
  }, []);

  const handleStart = () => {
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      setEmailError('Please enter a valid email address.');
      return;
    }
    setEmailError('');
    setIsGameStarted(true);
  };

  const handleNextQuestion = useCallback(() => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1);
      setGameState(GameState.PLAYING);
      setSelectedAnswerIndex(null);
      setTimer(QUESTION_TIMER_DURATION);
    } else {
      setGameState(GameState.FINISHED);
    }
  }, [currentQuestionIndex, questions.length]);

  const handleSelectAnswer = useCallback((answerIndex: number) => {
    if (gameState !== GameState.PLAYING) return;

    const isCorrect = answerIndex === currentQuestion.correctAnswerIndex;

    if (isCorrect) {
      setScore((prev) => prev + 10);
    }

    setQuestions((prevQuestions) =>
      prevQuestions.map((q, index) =>
        index === currentQuestionIndex
          ? { ...q, status: isCorrect ? QuestionStatus.CORRECT : QuestionStatus.INCORRECT }
          : q
      )
    );
    
    let remaining = 100;
    const finalData = currentQuestion.options.map((option, i, arr) => {
      let val;
      if (i === arr.length - 1) {
        val = remaining;
      } else {
        val = Math.floor(Math.random() * (remaining/2));
        remaining -= val;
      }
      return {
          name: `${String.fromCharCode(65 + i)}. ${option}`,
          value: val,
      };
    }).sort(() => Math.random() - 0.5);

    setChartData(finalData);

    setSelectedAnswerIndex(answerIndex);
    setGameState(GameState.ANSWERED);
    setTimer(RESULTS_VIEW_DURATION);
  }, [gameState, currentQuestion, currentQuestionIndex]);

  useEffect(() => {
    if (!isGameStarted || gameState === GameState.FINISHED) return;

    const interval = setInterval(() => {
      setTimer((prev) => prev - 1);
    }, 1000);

    if (timer === 0) {
      if (gameState === GameState.PLAYING) {
        handleSelectAnswer(-1); 
      } else if (gameState === GameState.ANSWERED) {
        handleNextQuestion();
      }
    }

    return () => clearInterval(interval);
  }, [timer, gameState, handleSelectAnswer, handleNextQuestion, isGameStarted]);

  const getAnswerButtonClass = (index: number) => {
    const baseClass = "w-full text-white font-bold py-4 px-6 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-between";
    if (gameState === GameState.ANSWERED) {
      if (index === currentQuestion.correctAnswerIndex) {
        return `${baseClass} bg-green-600 border-2 border-green-300`;
      }
      if (index === selectedAnswerIndex) {
        return `${baseClass} bg-red-600 border-2 border-red-300`;
      }
      return `${baseClass} bg-gray-600/70 border-2 border-transparent cursor-not-allowed`;
    }
    return `${baseClass} bg-gray-700/80 hover:bg-gray-600/80 border-2 border-transparent`;
  };

  if (!isGameStarted) {
    return (
      <div className="min-h-screen w-full flex flex-col items-center justify-center p-4">
        <main className="w-full max-w-md text-center">
          <h1 className="text-4xl font-bold mb-8 text-slate-800">Online quizzing application</h1>
          <div className="flex flex-col items-center space-y-4">
            <input
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                if (emailError) setEmailError('');
              }}
              placeholder="Enter Your Mail"
              className="w-full max-w-sm px-4 py-3 rounded-md bg-white text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-300 shadow-inner"
              aria-label="Enter Your Mail"
              required
            />
            <button
              onClick={handleStart}
              className="w-36 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-md shadow-md transition-all duration-300"
            >
              Start
            </button>
            {emailError && <p className="text-red-500 mt-2 text-sm">{emailError}</p>}
          </div>
        </main>
        <footer className="absolute bottom-8 text-center w-full">
          <p className="text-sm text-slate-600 font-medium mb-2">Knowledge Partner</p>
          <div className="flex justify-center">
            <img src="/images/logos/Mediccapress-partner.png" alt="Medicca Press Logo" className="h-12" />
          </div>
        </footer>
      </div>
    );
  }

  const renderContent = () => {
    if (gameState === GameState.FINISHED) {
      return (
        <div className="w-full h-full flex flex-col items-center justify-center bg-slate-800/80 backdrop-blur-md rounded-2xl text-white p-8">
          <h2 className="text-5xl font-bold mb-4">Quiz Over!</h2>
          <p className="text-3xl">Your Final Score is:</p>
          <p className="text-8xl font-bold mt-4 text-green-400">{score}</p>
        </div>
      );
    }

    return (
      <div className="flex flex-col h-full space-y-4">
        <div className="h-1/3 relative flex items-center justify-center">
          {gameState === GameState.ANSWERED ? (
             <RecentAnswersChart data={chartData} correctAnswerName={`${String.fromCharCode(65 + currentQuestion.correctAnswerIndex)}. ${currentQuestion.options[currentQuestion.correctAnswerIndex]}`} />
          ) : (
             <div className="w-full h-full rounded-lg overflow-hidden shadow-lg">
                <img src="/images/logos/streamer image.png" alt="Quiz Host" className="w-full h-full object-cover" />
                <div className="absolute top-2 right-2 bg-red-600 text-white px-3 py-1 text-sm font-bold rounded-md flex items-center space-x-2">
                    <span>LIVE</span>
                    <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
                    <span>4,378</span>
                </div>
             </div>
          )}
        </div>

        <div className="flex justify-center items-center">
            <div className={`relative w-28 h-28 flex items-center justify-center rounded-full bg-slate-800/70 shadow-2xl border-4 ${gameState === GameState.PLAYING ? 'border-red-500' : (selectedAnswerIndex === currentQuestion.correctAnswerIndex ? 'border-green-500' : 'border-red-600')}`}>
                <p className="text-white text-5xl font-bold">{timer}</p>
            </div>
        </div>

        <div className="flex-grow flex flex-col justify-between">
            <p className="text-center text-2xl font-semibold bg-slate-800/60 text-white py-4 px-6 rounded-lg shadow-lg">
                {currentQuestion.question}
            </p>
            <div className="grid grid-cols-2 gap-4">
                {currentQuestion.options.map((option, index) => (
                    <button
                        key={index}
                        onClick={() => handleSelectAnswer(index)}
                        disabled={gameState === GameState.ANSWERED}
                        className={getAnswerButtonClass(index)}
                    >
                        <span>{String.fromCharCode(65 + index)}. {option}</span>
                         {gameState === GameState.ANSWERED && index === currentQuestion.correctAnswerIndex && <CheckIcon className="w-8 h-8"/>}
                    </button>
                ))}
            </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="min-h-screen w-full flex items-center justify-center p-4 bg-black/30">
        <div className="w-full max-w-7xl h-[90vh] flex flex-row gap-6">
            <QuestionsPanel questions={questions} currentQuestionIndex={currentQuestionIndex} score={score} />
            <div className="flex-grow h-full">{renderContent()}</div>
            <Leaderboard players={FASTEST_FINGERS} />
        </div>
    </div>
  );
};

export default App;
